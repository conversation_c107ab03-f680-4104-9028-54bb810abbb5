import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import '../styles/components/Header.css';
import logoEsvc from '../assets/logo-esvc.png';
import DashboardModeToggle from './DashboardModeToggle';
import { useDemoState } from '../context/DemoStateContext';

interface HeaderProps {
  onNavigateToSignUp?: () => void;
  onNavigateToLogin?: () => void;
  onNavigateToLanding?: () => void;
}

const Header: React.FC<HeaderProps> = ({
  onNavigateToSignUp,
  onNavigateToLogin,
  onNavigateToLanding
}) => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const { userData } = useDemoState();

  // Check if we should show the dashboard mode toggle (only on user dashboard pages)
  const shouldShowDashboardToggle = location.pathname.includes('/user-dashboard');

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  return (
    <header className="header">
      <div className="container">
        <div className="header-content">
          {/* Logo */}
          <div className="logo" onClick={onNavigateToLanding} style={{ cursor: 'pointer' }}>
            <img src={logoEsvc} alt="ESVC Logo" className="logo-image" />
          </div>

          {/* Desktop Navigation */}
          <nav className="nav desktop-nav">
            <button onClick={onNavigateToLanding} className="nav-link active">Home</button>
            <button onClick={() => navigate('/user-dashboard/stake-esvc')} className="nav-link">Stake ESVC</button>
            <button onClick={() => navigate('/user-dashboard/get-funding')} className="nav-link">Get Funding</button>
            <button onClick={() => navigate('/trade-challenge')} className="nav-link">Trade Challenge</button>
            <button onClick={() => navigate('/contact-us')} className="nav-link">Contact Us</button>
          </nav>

          {/* Desktop Action Buttons */}
          <div className="header-actions desktop-actions">
            <button className="btn-secondary" onClick={onNavigateToLogin}>Login</button>
            <button className="btn-primary" onClick={onNavigateToSignUp}>Get Started</button>
          </div>

          {/* Mobile Hamburger Menu */}
          <button className="mobile-menu-toggle" onClick={toggleMobileMenu}>
            <span className="hamburger-line"></span>
            <span className="hamburger-line"></span>
            <span className="hamburger-line"></span>
          </button>
        </div>

        {/* Mobile Menu */}
        <div className={`mobile-menu ${isMobileMenuOpen ? 'mobile-menu-open' : ''}`}>
          <nav className="mobile-nav">
            <button onClick={onNavigateToLanding} className="mobile-nav-link">Home</button>
            <a href="#stake" className="mobile-nav-link">Stake ESVC</a>
            <a href="#funding" className="mobile-nav-link">Get Funding</a>
            <button onClick={() => navigate('/trade-challenge')} className="mobile-nav-link">Trade Challenge</button>
            <a href="#contact" className="mobile-nav-link">Contact Us</a>
          </nav>
          <div className="mobile-actions">
            <button className="btn-primary mobile-btn" onClick={onNavigateToSignUp}>Get Started</button>
            <button className="mobile-login" onClick={onNavigateToLogin}>Login</button>
          </div>
        </div>
      </div>

      {/* Dashboard Mode Toggle - Show only on user dashboard pages and when user is logged in */}
      {shouldShowDashboardToggle && userData.isLoggedIn && (
        <DashboardModeToggle className="header-toggle" />
      )}
    </header>
  );
};

export default Header;
