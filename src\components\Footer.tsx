import React from 'react';
import { useNavigate } from 'react-router-dom';
import '../styles/components/Footer.css';
import whiteLogo from '../assets/esvc 2.png';
import facebookIcon from '../assets/facebook.png';
import twitterIcon from '../assets/twitter.png';
import instagramIcon from '../assets/instagram.png';
import linkedinIcon from '../assets/linkedin.png';

const Footer: React.FC = () => {
  const navigate = useNavigate();

  return (
    <footer className="footer">
      {/* Background blur gradients */}
      <div className="footer-blur-gradient footer-blur-gradient-1"></div>
      <div className="footer-blur-gradient footer-blur-gradient-2"></div>

      <div className="container">
        <div className="footer-content">
          {/* Logo */}
          <div className="footer-logo">
            <img src={whiteLogo} alt="ESVC" className="footer-logo-img" />
          </div>

          {/* Navigation */}
          <nav className="footer-nav">
            <button onClick={() => navigate('/')} className="footer-link">Home</button>
            <button onClick={() => navigate('/user-dashboard/stake-esvc')} className="footer-link">Stake ESVC</button>
            <button onClick={() => navigate('/user-dashboard/get-funding')} className="footer-link">Get Funding</button>
            <button onClick={() => navigate('/trade-challenge')} className="footer-link">Trade Challenge</button>
            <button onClick={() => navigate('/contact-us')} className="footer-link">Contact Us</button>
          </nav>

          {/* Social Media */}
          <div className="footer-social">
            <a href="#" className="social-link" aria-label="Instagram">
              <img src={instagramIcon} alt="Instagram" className="social-icon" />
            </a>
            <a href="#" className="social-link" aria-label="Facebook">
              <img src={facebookIcon} alt="Facebook" className="social-icon" />
            </a>
            <a href="#" className="social-link" aria-label="Twitter">
              <img src={twitterIcon} alt="Twitter" className="social-icon" />
            </a>
            <a href="#" className="social-link" aria-label="LinkedIn">
              <img src={linkedinIcon} alt="LinkedIn" className="social-icon" />
            </a>
          </div>
        </div>

        {/* Copyright */}
        <div className="footer-bottom">
          <p className="copyright">© 2025 ESV Capital. All rights reserved.</p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
